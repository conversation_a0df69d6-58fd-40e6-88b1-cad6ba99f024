# Redisson单服务器配置
singleServerConfig:
  # Redis服务器地址
  address: "redis://***************:6379"
  # 连接池大小
  connectionPoolSize: 64
  # 最小空闲连接数
  connectionMinimumIdleSize: 10
  # 连接超时时间(毫秒)
  connectTimeout: 10000
  # 命令等待超时时间(毫秒)
  timeout: 3000
  # 命令失败重试次数
  retryAttempts: 3
  # 命令重试发送时间间隔(毫秒)
  retryInterval: 1500
  # 密码
  password: "pass"
  # 数据库编号
  database: 0
  # 客户端名称
  clientName: "sfbx-redis-client"
  # 发布和订阅连接的最小空闲连接数
  subscriptionConnectionMinimumIdleSize: 1
  # 发布和订阅连接池大小
  subscriptionConnectionPoolSize: 50
  # 单个连接最大订阅数量
  subscriptionsPerConnection: 5
  # DNS监测时间间隔(毫秒)
  dnsMonitoringInterval: 5000
