package com.itheima.sfbx.framework.redis.serializer;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.util.Assert;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 兼容的Jackson2JsonRedisSerializer
 * 用于处理旧数据的反序列化问题
 */
public class CompatibleJackson2JsonRedisSerializer implements RedisSerializer<Object> {

    public static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    private ObjectMapper objectMapper = new ObjectMapper();

    public CompatibleJackson2JsonRedisSerializer() {
        this.objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        this.objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
    }

    public CompatibleJackson2JsonRedisSerializer(ObjectMapper objectMapper) {
        Assert.notNull(objectMapper, "ObjectMapper must not be null!");
        this.objectMapper = objectMapper;
    }

    @Override
    public byte[] serialize(Object source) throws SerializationException {
        if (source == null) {
            return new byte[0];
        }
        try {
            return this.objectMapper.writeValueAsBytes(source);
        } catch (Exception ex) {
            throw new SerializationException("Could not write JSON: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Object deserialize(byte[] source) throws SerializationException {
        if (source == null || source.length == 0) {
            return null;
        }
        try {
            String jsonString = new String(source, DEFAULT_CHARSET);
            
            // 处理旧的类路径问题
            if (jsonString.contains("com.itheima.bolee.framework.commons.dto.dict")) {
                // 替换旧的类路径为新的类路径
                jsonString = jsonString.replace(
                    "com.itheima.bolee.framework.commons.dto.dict.DataDictVO",
                    "com.itheima.sfbx.dict.vo.DataDictVO"
                );
                jsonString = jsonString.replace(
                    "com.itheima.bolee.framework.commons.dto.dict.PlacesVO",
                    "com.itheima.sfbx.dict.vo.PlacesVO"
                );
            }
            
            return this.objectMapper.readValue(jsonString, Object.class);
        } catch (Exception ex) {
            // 如果反序列化失败，返回null而不是抛出异常
            // 这样可以让缓存重新加载数据
            return null;
        }
    }

    public void setObjectMapper(ObjectMapper objectMapper) {
        Assert.notNull(objectMapper, "ObjectMapper must not be null!");
        this.objectMapper = objectMapper;
    }

    protected TypeFactory getTypeFactory() {
        return this.objectMapper.getTypeFactory();
    }
}
