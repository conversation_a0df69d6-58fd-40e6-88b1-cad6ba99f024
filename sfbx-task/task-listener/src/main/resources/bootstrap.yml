#服务配置
server:
  #端口
  port: 7076
  #服务编码
  tomcat:
    uri-encoding: UTF-8
spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  profiles:
    active: dev
  #应用配置
  application:
    #应用名称
    name: task-listener
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.200.129:8848 # nacos注册中心
        group: SEATA_GROUP
        service: ${spring.application.name}
      config:
        server-addr: 192.168.200.129:8848 # nacos配置中心地址
        group: SEATA_GROUP
        file-extension: yml
        shared-configs: # 共享配置
          # 临时注释RabbitMQ配置
          # - data-id: shared-stream-rabbit-basic.yml #配置文件名-DataId
          #   group: SEATA_GROUP
          #   refresh: false
          # - data-id: shared-stream-rabbit-sink-sms.yml #配置文件名-DataId
          #   group: SEATA_GROUP
          #   refresh: false
          # - data-id: shared-stream-rabbit-sink-log.yml #配置文件名-DataId
          #   group: SEATA_GROUP
          #   refresh: false
          # - data-id: shared-stream-rabbit-sink-file.yml #配置文件名-DataId
          #   group: SEATA_GROUP
          #   refresh: false
          # - data-id: shared-stream-rabbit-sink-warranty.yml #配置文件名-DataId
          #   group: SEATA_GROUP
          #   refresh: false
          # - data-id: shared-stream-rabbit-sink-trade.yml #配置文件名-DataId
          #   group: SEATA_GROUP
          #   refresh: false
logging:
  config: classpath:logback.xml
